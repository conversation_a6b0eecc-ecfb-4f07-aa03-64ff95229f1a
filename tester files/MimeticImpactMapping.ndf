// Ne pas éditer, ce fichier est généré par MimeticImpactMappingFileWriter


FxImpact2A38Autocanon_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_2A38_Autocanon_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_2A38_Autocanon_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_2A38_Autocanon_3 ),
    ]
)
FxImpactFK20Autocanon_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_FK20_Autocanon_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_FK20_Autocanon_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_FK20_Autocanon_3 ),
    ]
)
FxImpactGAU8Gatling_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_GAU8_Gatling_8 ),
    ]
)
FxImpactM168Gatling_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M168_Gatling_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M168_Gatling_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M168_Gatling_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M168_Gatling_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M168_Gatling_5 ),
    ]
)
FxImpactM197Gatling_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M197_Gatling_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M197_Gatling_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M197_Gatling_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M197_Gatling_4 ),
    ]
)
FxImpactM61A1Vulcan_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M61A1_Vulcan_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M61A1_Vulcan_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M61A1_Vulcan_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_M61A1_Vulcan_4 ),
    ]
)
FxImpactMK27Canon_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_MK27_Canon_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_MK27_Canon_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_MK27_Canon_3 ),
    ]
)
FxImpactVehiculeGAU8Gatling is ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_Vehicule_GAU8_Gatling )
FxImpactVehiculeM61A1Vulcan_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_Vehicule_M61A1_Vulcan_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_Vehicule_M61A1_Vulcan_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_Vehicule_M61A1_Vulcan_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_Vehicule_M61A1_Vulcan_4 ),
    ]
)
FxImpactBalleSniperEau is ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balle_Sniper_Eau )
FxImpactBalleSniperLourdSol_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balle_Sniper_Lourd_Sol_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balle_Sniper_Lourd_Sol_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balle_Sniper_Lourd_Sol_3 ),
    ]
)
FxImpactBalleSniperSol is ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balle_Sniper_Sol )
FxImpactBallesCal50Eau_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balles_Cal50_Eau_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_balles_Cal50_Eau_2 ),
    ]
)
FxImpactBat2A38Autocanon_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_bat_2A38_Autocanon_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_bat_2A38_Autocanon_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/Fx_impact_bat_2A38_Autocanon_3 ),
    ]
)
OrientationWrapperFxImpactM168Gatling is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_Fx_impact_M168_Gatling )
OrientationWrapperFxImpactM197Gatling is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_Fx_impact_M197_Gatling )
OrientationWrapperFxImpactVehicule2A38Autocanon is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_Fx_impact_Vehicule_2A38_Autocanon )
OrientationWrapperFxImpactVehiculeM168Gatling is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_Fx_impact_Vehicule_M168_Gatling )
OrientationWrapperFxImpactVehiculeM197Gatling is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_Fx_impact_Vehicule_M197_Gatling )
OrientationWrapperFxImpactEauAvionGros is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_eau_avion_gros )
OrientationWrapperFxImpactSolAvion is ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion )
OrientationWrapperFxImpactSolAvionGros__ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_11 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_12 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/OrientationWrapper_fx_impact_sol_avion_gros_9 ),
    ]
)
FxImpactSolMRFMinenraumfahrzeug_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_Impact_sol_MRF_Minenraumfahrzeug_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Impact_sol_MRF_Minenraumfahrzeug_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Impact_sol_MRF_Minenraumfahrzeug_3 ),
    ]
)
FxOuvertureBombeCluster_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombe_Cluster_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombe_Cluster_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombe_Cluster_3 ),
    ]
)
FxOuvertureBombletesNoAirBurstMW1_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombletes_NoAirBurst_MW1_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombletes_NoAirBurst_MW1_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Bombletes_NoAirBurst_MW1_3 ),
    ]
)
FxOuvertureRoquetteCluster_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Cluster_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Cluster_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Cluster_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Cluster_4 ),
    ]
)
FxOuvertureRoquetteNapalm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Napalm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Napalm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_Ouverture_Roquette_Napalm_3 ),
    ]
)
FxDestructionUniteMBT_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_11 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_12 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_13 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_14 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_15 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_MBT_9 ),
    ]
)
FxDestructionUnitePetite_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_destruction_unite_petite_9 ),
    ]
)
FxImpactDCAM168GatlingAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_air_4 ),
    ]
)
FxImpactDCAM168GatlingVehiculeAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_vehicule_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_vehicule_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_vehicule_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M168_Gatling_vehicule_air_4 ),
    ]
)
FxImpactDCAM197GatlingAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_air_4 ),
    ]
)
FxImpactDCAM197GatlingVehiculeAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_vehicule_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_vehicule_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_vehicule_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_M197_Gatling_vehicule_air_4 ),
    ]
)
FxImpactDCAProximityFuzeAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_air_4 ),
    ]
)
FxImpactDCAProximityFuzeVehiculeAir_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_vehicule_air_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_vehicule_air_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_vehicule_air_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_Proximity_Fuze_vehicule_air_4 ),
    ]
)
FxImpactDCAAirGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_air_gros )
FxImpactDCAAirPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_air_petit )
FxImpactDCAAirTresGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_DCA_air_tres_gros )
FxImpactLanceFlammes_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_Lance_Flammes_9 ),
    ]
)
FxImpactApVehiculeAirGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_ap_vehicule_air_gros )
FxImpactBalleBatimentTresPetitFMSection_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_FM_section_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_FM_section_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_FM_section_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_FM_section_4 ),
    ]
)
FxImpactBalleBatimentTresPetitMMGSection_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_MMG_section_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_MMG_section_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_MMG_section_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_MMG_section_4 ),
    ]
)
FxImpactBalleBatimentTresPetitMitrailleuse_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_mitrailleuse_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_mitrailleuse_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_mitrailleuse_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_batiment_tres_petit_mitrailleuse_4 ),
    ]
)
FxImpactBalleEauMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_eau_moyen )
FxImpactBalleSolTresPetitFMSection_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_FM_section_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_FM_section_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_FM_section_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_FM_section_4 ),
    ]
)
FxImpactBalleSolTresPetitMG3Section_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MG3_section_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MG3_section_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MG3_section_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MG3_section_5 ),
    ]
)
FxImpactBalleSolTresPetitMMGSection_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MMG_section_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MMG_section_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MMG_section_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MMG_section_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_MMG_section_5 ),
    ]
)
FxImpactBalleSolTresPetitMitrailleuse_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_mitrailleuse_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_mitrailleuse_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_mitrailleuse_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_mitrailleuse_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_sol_tres_petit_mitrailleuse_5 ),
    ]
)
FxImpactBalleVehiculeMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_moyen )
FxImpactBalleVehiculeMoyen_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_moyen_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_moyen_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_moyen_3 ),
    ]
)
FxImpactBalleVehiculePetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_petit )
FxImpactBalleVehiculeTresPetitMitrailleuse_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_tres_petit_mitrailleuse_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_tres_petit_mitrailleuse_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_tres_petit_mitrailleuse_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balle_vehicule_tres_petit_mitrailleuse_5 ),
    ]
)
FxImpactBallesAirTresPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_air_tres_petit_6 ),
    ]
)
FxImpactBallesVehiculeAirTresPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_balles_vehicule_air_tres_petit_6 ),
    ]
)
FxImpactBatApGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_ap_gros )
FxImpactBatApPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_ap_petit )
FxImpactBatHeGros_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_gros_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_gros_4 ),
    ]
)
FxImpactBatHeMoyen_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_moyen_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_moyen_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_moyen_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_moyen_4 ),
    ]
)
FxImpactBatHePetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_petit_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_bat_he_petit_4 ),
    ]
)
FxImpactBatimentCal50Solo_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_batiment_Cal50_solo_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_batiment_Cal50_solo_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_batiment_Cal50_solo_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_batiment_Cal50_solo_4 ),
    ]
)
FxImpactDestructionUniteRavitaillement is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_destruction_unite__ravitaillement )
FxImpactDestructionUniteRavitaillement_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_destruction_unite__ravitaillement_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_destruction_unite__ravitaillement_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_destruction_unite__ravitaillement_3 ),
    ]
)
FxImpactDestructionUniteOld is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_destruction_unite_old )
FxImpactEauHEGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_eau_HE_gros )
FxImpactEauHEMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_eau_HE_moyen )
FxImpactEauHEPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_eau_HE_petit )
FxImpactEauHeTresgros2 is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_eau_he_tresgros2 )
FxImpactEauHelico_ is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_eau_helico_1 )
FxImpactHeVehiculeAirGros__ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_gros_01 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_gros_02 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_gros_03 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_gros_04 ),
    ]
)
FxImpactHeVehiculeAirMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_moyen )
FxImpactHeVehiculeAirPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_petit )
FxImpactHeVehiculeAirPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_petit_4 ),
    ]
)
FxImpactHeVehiculeAirTresGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_he_vehicule_air_tres_gros )
FxImpactMissileAir is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_air )
FxImpactMissileAirGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_air_gros )
FxImpactMissileAirMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_air_moyen )
FxImpactMissileAirPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_air_petit )
FxImpactMissileAirTresGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_air_tres_gros )
FxImpactMissileSol is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_sol )
FxImpactMissileSolTopHEAT is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_sol_Top_HEAT )
FxImpactMissileSolGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_sol_gros )
FxImpactMissileVehiculeGros_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_gros_3 ),
    ]
)
FxImpactMissileVehiculeMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_moyen )
FxImpactMissileVehiculePetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit )
FxImpactMissileVehiculePetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_3 ),
    ]
)
FxImpactMissileVehiculePetitTopHEAT_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_Top_HEAT_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_Top_HEAT_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_missile_vehicule_petit_Top_HEAT_3 ),
    ]
)
FxImpactObusVehiculeGros_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_gros_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_gros_4 ),
    ]
)
FxImpactObusVehiculeMoyen_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_moyen_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_moyen_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_moyen_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_obus_vehicule_moyen_4 ),
    ]
)
FxImpactSolAGS17HE_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_AGS17_HE_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_AGS17_HE_2 ),
    ]
)
FxImpactSolAPGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_AP_gros )
FxImpactSolAutocanonHE_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Autocanon_HE_8 ),
    ]
)
FxImpactSolBombFAB250_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_FAB250_8 ),
    ]
)
FxImpactSolBombGBU12_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_GBU12_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_GBU12_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_GBU12_3 ),
    ]
)
FxImpactSolBombKAB1500_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_KAB1500_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_KAB1500_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_KAB1500_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_KAB1500_4 ),
    ]
)
FxImpactSolBombM117_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_M117_8 ),
    ]
)
FxImpactSolBombMk77_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk77_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk77_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk77_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk77_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk77_5 ),
    ]
)
FxImpactSolBombMk82_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk82_9 ),
    ]
)
FxImpactSolBombMk83_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk83_9 ),
    ]
)
FxImpactSolBombMk84_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_Mk84_8 ),
    ]
)
FxImpactSolBombODAB_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomb_ODAB_8 ),
    ]
)
FxImpactSolBombletNoAirBurstMW1_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomblet_NoAirBurst_MW1_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Bomblet_NoAirBurst_MW1_2 ),
    ]
)
FxImpactSolCal50Solo_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Cal50_solo_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Cal50_solo_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Cal50_solo_3 ),
    ]
)
FxImpactSolClusterMK20RockeyeIISalve_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Cluster_MK20_Rockeye_II_Salve_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Cluster_MK20_Rockeye_II_Salve_2 ),
    ]
)
FxImpactSolHE122Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_122mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_122mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_122mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_122mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_122mm_5 ),
    ]
)
FxImpactSolHEBuratino220Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Buratino_220mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Buratino_220mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Buratino_220mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Buratino_220mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Buratino_220mm_5 ),
    ]
)
FxImpactSolHEM185155Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M185_155mm_9 ),
    ]
)
FxImpactSolHEM201203Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M201_203mm_9 ),
    ]
)
FxImpactSolHEM270110Mm130MmCluster_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_110mm_130mm_Cluster_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_110mm_130mm_Cluster_2 ),
    ]
)
FxImpactSolHEM270227Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_9 ),
    ]
)
FxImpactSolHEM270227MmCluster_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_Cluster_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_Cluster_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_M270_227mm_Cluster_3 ),
    ]
)
FxImpactSolHEMortierM30107Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_Mortier_M30_107mm_8 ),
    ]
)
FxImpactSolHEGros is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros )
FxImpactSolHEGros_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_gros_6 ),
    ]
)
FxImpactSolHEMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_moyen )
FxImpactSolHEPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit )
FxImpactSolHEPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_petit_9 ),
    ]
)
FxImpactSolHERoquette110Mm130Mm_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_roquette_110mm_130mm_8 ),
    ]
)
FxImpactSolHETresGros_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_HE_tres_gros_8 ),
    ]
)
FxImpactSolLightS5_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Light_S5_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Light_S5_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Light_S5_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Light_S5_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Light_S5_5 ),
    ]
)
FxImpactSolM242APFDS is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M242_APFDS )
FxImpactSolM261Hydras_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_11 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_12 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Hydras_9 ),
    ]
)
FxImpactSolM261LightHydras_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_10 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_11 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_12 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_8 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_M261_Light_Hydras_9 ),
    ]
)
FxImpactSolNapalmRoquette110Mm130Mm_ is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Napalm_roquette_110mm_130mm_1 )
FxImpactSolS13_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_6 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_7 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S13_8 ),
    ]
)
FxImpactSolS5_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S5_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S5_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S5_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S5_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_S5_5 ),
    ]
)
FxImpactSolSmokeDispenser_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Dispenser_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Dispenser_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Dispenser_3 ),
    ]
)
FxImpactSolSmokeMortarShell_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Mortar_Shell_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Mortar_Shell_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Mortar_Shell_3 ),
    ]
)
FxImpactSolSmokeShell_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Shell_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Shell_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Smoke_Shell_3 ),
    ]
)
FxImpactSolTacticalNuke is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_Tactical_Nuke )
FxImpactSolHelicoMoyen_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_moyen_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_moyen_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_moyen_3 ),
    ]
)
FxImpactSolHelicoPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_petit_3 ),
    ]
)
FxImpactSolHelicoTresPetit_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_tres_petit_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_tres_petit_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_sol_helico_tres_petit_3 ),
    ]
)
FxImpactVehiculeM242APFDS_ is TRandomHappening
(
    Alternatives = [
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_1 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_2 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_3 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_4 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_5 ),
        ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_APFDS_6 ),
    ]
)
FxImpactVehiculeM242HEIT is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M242_HEIT )
FxImpactVehiculeM261Hydras is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_M261_Hydras )
FxImpactVehiculeHeTresgros2 is ImpactWrapper( Action = $/GFX/GameFx/fx_impact_vehicule_he_tresgros2 )
FxMortInfanterie is ImpactWrapper( Action = $/GFX/GameFx/fx_mort_infanterie )
FxRebondObusGros is ImpactWrapper( Action = $/GFX/GameFx/fx_rebond_obus_gros )
FxRebondObusMoyen is ImpactWrapper( Action = $/GFX/GameFx/fx_rebond_obus_moyen )
FxRebondObusPetit is ImpactWrapper( Action = $/GFX/GameFx/fx_rebond_obus_petit )

AllImpactHappenings is MAP[
  ([ 'Artillerie2A18122Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHE122Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'Artillerie2A18122MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'Artillerie2A60120Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHE122Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHE122Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'Artillerie2A60120MmHEAT' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEGros_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'Artillerie2A60120MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'ArtillerieM185155Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM185155Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEM185155Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM185155Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM185155Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM185155Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'ArtillerieM185155MmNUKE' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , FxImpactSolTacticalNuke ),
      ( EImpactSurface/Water , FxImpactSolTacticalNuke ),
      ( EImpactSurface/Air , FxImpactSolTacticalNuke ),
      ( EImpactSurface/Wall , FxImpactSolTacticalNuke ),
      ( EImpactSurface/Vehicle , FxImpactSolTacticalNuke ),
      ( EImpactSurface/FlyingVehicle , FxImpactSolTacticalNuke ),
      ( EImpactSurface/Missile , nil ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'ArtillerieM185155MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'ArtillerieM201203Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEM201203Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'ArtillerieM201203MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombMatra250Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk82_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombMatra400Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk83_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeBLG66' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureBombeCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeFAB250' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombFAB250_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombFAB250_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombFAB250_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombFAB250_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombFAB250_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeFAB500' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk83_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeGBU24' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk84_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeMk20RockeyeII' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureBombeCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolClusterMK20RockeyeIISalve_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeMk77' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk77_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk77_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombM117_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombM117_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombM117_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeMk81' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk82_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeMk82' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk82_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeMk83' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk83_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk83_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombeODAB' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombODAB_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombODAB_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombODAB_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombODAB_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombODAB_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'BombletsMW1' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombletNoAirBurstMW1_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolBombletNoAirBurstMW1_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureBombletesNoAirBurstMW1_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombletNoAirBurstMW1_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombletNoAirBurstMW1_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombletNoAirBurstMW1_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonAPM48' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAPGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolAPGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonHEM48' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosAPFDSM1Abrams' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAPGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolAPGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosAPFDST80' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAPGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolAPGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosAPFDSU5TSDouille' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAPGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolAPGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApGros, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHEAVRE' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEMortierM30107Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHEM1Abrams' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHESHERIDAN' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEMortierM30107Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHET80' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHETresGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHETresGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHETresGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHEU5TSDouille' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonGrosHEU5TSDouilleAA' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirTresGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirTresGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
    ])),
  ([ 'CanonMoyenAPPT76' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonMoyenBOFORHEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirGros, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirGros__, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonMoyenHEBMP3' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_grosBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonMoyenHEPT76' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactApVehiculeAirGros, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonMoyenS60HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirGros, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactObusVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirGros__, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'CanonPetit24A2AP' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit24A2APAIR' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit24A2HE' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit24A2HEAIR' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit2A38HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpact2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAProximityFuzeAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBat2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehicule2A38Autocanon, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAProximityFuzeVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit2A72APT' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetit2A72HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitAPZ23HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpact2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBat2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehicule2A38Autocanon, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitDualFK20HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpact2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBat2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehicule2A38Autocanon, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitFK20HEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactFK20Autocanon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBat2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehicule2A38Autocanon, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitM230AP' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitM230HEDP' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitM242APFSDST' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitM242HEIT' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitOerlikonGPFHEFI' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpact2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAProximityFuzeAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBat2A38Autocanon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehicule2A38Autocanon, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAProximityFuzeVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitRARDENAP' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM242APFDS, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatApPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242APFDS_, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'CanonPetitRARDENHE' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAutocanonHE_, $/GFX/Sound/SoundHappening_ImpactCanon_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAAirPetit, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactCanon_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'DestructionInfanterie' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxMortInfanterie, $/GFX/Sound/SoundHappening_Mort_infanterie ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUnite' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteAvion' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUnite_Avion ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUnite_Avion_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUnite_Avion ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUnite_Avion ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvion, $/GFX/Sound/SoundHappening_DestructionUnite_Avion ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteAvionGros' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros_Avion ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactEauAvionGros, $/GFX/Sound/SoundHappening_DestructionUnite_Avion_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros_Avion ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros_Avion ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactSolAvionGros__, $/GFX/Sound/SoundHappening_DestructionUniteGros_Avion ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteHelicoLeger' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHelico_, $/GFX/Sound/SoundHappening_DestructionHelico_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteHelicoMoyen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionHelico ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHelico_, $/GFX/Sound/SoundHappening_DestructionHelico_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionHelico ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionHelico ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoMoyen_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteHelicoUltraLeger' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHelico_, $/GFX/Sound/SoundHappening_DestructionHelico_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactSolHelicoTresPetit_, $/GFX/Sound/SoundHappening_DestructionHelicoPetit ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteRavitaillement' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement_, $/GFX/Sound/SoundHappening_DestructionUnite_Ravitaillement ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteOld, $/GFX/Sound/SoundHappening_DestructionUnite_Ravitaillement ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement, $/GFX/Sound/SoundHappening_DestructionUnite_Ravitaillement ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement, $/GFX/Sound/SoundHappening_DestructionUnite_Ravitaillement ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactDestructionUniteRavitaillement, $/GFX/Sound/SoundHappening_DestructionUnite_Ravitaillement ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUniteMoyen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxDestructionUniteMBT_, $/GFX/Sound/SoundHappening_DestructionUnite ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'DestructionUnitePetit' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUnitePetit ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUnitePetit ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUnitePetit ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUnitePetit ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUniteGros ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxDestructionUnitePetite_, $/GFX/Sound/SoundHappening_DestructionUnitePetit ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'FusilMitrailleurAK762MmGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBallesSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBallesSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'FusilMitrailleurM16556Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBallesSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBallesSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'FusileSniper762' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSniperSol, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleSniperEau, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBallesBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculePetit, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculePetit, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'FusileSniperLourd127' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSniperLourdSol_, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleSniperEau, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitFMSection_, $/GFX/Sound/SoundHappening_ImpactBallesBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'GAU8Gatling' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactGAU8Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol_GAU ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactGAU8Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol_GAU ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeGAU8Gatling, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol_GAU ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'GrenadeANM16Smoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'GrenadeSatchem' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEMortierM30107Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'GrenadeStielhandgranate' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirMoyen, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'GrenadeVehicleSmokeX8' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeDispenser_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeDispenser_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeDispenser_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeDispenser_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeDispenser_, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit, $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactGrenadeMainSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'Gsh23' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMK27Canon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactMK27Canon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'Gsh301' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'LanceGrenadeAGS17' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolAGS17HE_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolAGS17HE_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM242HEIT, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'Lanceflamme' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , FxImpactLanceFlammes_ ),
      ( EImpactSurface/Water , FxImpactLanceFlammes_ ),
      ( EImpactSurface/Air , FxImpactLanceFlammes_ ),
      ( EImpactSurface/Wall , FxImpactLanceFlammes_ ),
      ( EImpactSurface/Vehicle , FxImpactLanceFlammes_ ),
      ( EImpactSurface/FlyingVehicle , FxImpactLanceFlammes_ ),
      ( EImpactSurface/Missile , nil ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'M134Gatling' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM168Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'M168Gatling' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM168Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'M197Gatling' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM197Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactM197Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'M61A1Vulcan' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'MIssileAA9M311' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirMoyen ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirMoyen, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_moyen ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAAAMRAAM' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSolGros, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAABUKM1' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSolGros, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAAKRUG3M8M' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirTresGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_tres_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_ImpactSAM_tres_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirTresGros, $/GFX/Sound/SoundHappening_ImpactSAM_tres_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_tres_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAAMIM72C' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirMoyen ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirMoyen, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAASIDEWINDER' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirMoyen ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirMoyen, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAASPARROW' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSolGros, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MIssileAATOR' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSolGros, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirGros, $/GFX/Sound/SoundHappening_ImpactSAM_gros ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_gros ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MauserBK27' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMK27Canon_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactMK27Canon_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM61A1Vulcan_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM197GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'MissileAA9K38Igla' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirMoyen ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileAirPetit, $/GFX/Sound/SoundHappening_ImpactSAM_moyen ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactSAM_moyen ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MissileATPetit' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEPetit, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGen1' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGen2' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGen2TopHEAT' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSolTopHEAT, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetitTopHEAT_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGen3' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGen4' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileAGMGros' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAirGros ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirGros__, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileATGMGen1' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileATGMGen2' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileATGMGen3' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactMissileSol, $/GFX/Sound/SoundHappening_ImpactMissileAT_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEMoyen, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactMissileAir ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculeGros_, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMissileAT_moyenVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactMissileAT_grosVehicule ] ) ),
    ])),
  ([ 'MissileBombeGBU1000Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk84_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk84_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MissileBombeGBU1500Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombKAB1500_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombKAB1500_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombKAB1500_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombKAB1500_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombKAB1500_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MissileBombeGBU250Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombMk82_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombMk82_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MissileBombeGBU500Kg' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolBombGBU12_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolBombGBU12_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolBombGBU12_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombGBU12_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolBombGBU12_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Mk84_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseCal144ExterneGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Air , nil ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatimentCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBallesBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseCal50Externe' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Air , nil ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatimentCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBallesBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseCal50ExterneGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBalleEauMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_BalleSol_Sniper ] ) ),
      ( EImpactSurface/Air , nil ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatimentCal50Solo_, $/GFX/Sound/SoundHappening_ImpactBallesBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit_, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBallesVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseM240Externe' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseM240ExterneGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseM240Interne' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseM240InterneGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseMG3Inf' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMG3Section_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_MG_3 ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_MG_3 ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMG3Section_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseMG3Externe' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMG3Section_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_Short_MG_3 ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseMG3Interne' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMG3Section_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_Short_MG_3 ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeTresPetitMitrailleuse_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseMMGInf' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_SAW ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_SAW ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MitrailleuseMMGInfGreen' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_SAW ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_ImpactBalles_burstSol_SAW ] ) ),
      ( EImpactSurface/Air , FxImpactBallesAirTresPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBalleBatimentTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactBalleSolTresPetitMMGSection_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactBallesVehiculeAirTresPetit_, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Missile , TCompositeHappening( SubHappenings = [ FxImpactBalleVehiculeMoyen, $/GFX/Sound/SoundHappening_ImpactBalles_burstVehicule ] ) ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'MortierVasilek' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEMortierM30107Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusPetit, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
  ([ 'MortierM240240Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEM201203Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM201203Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
    ])),
  ([ 'MortierM240240MmCluster' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureRoquetteCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
    ])),
  ([ 'MortierM240240MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ),
    ])),
  ([ 'MortierM30107Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEMortierM30107Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEMortierM30107Mm_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'MortierM30107MmSmoke' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeMortarShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeMortarShell_, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolSmokeMortarShell_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeMortarShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolSmokeMortarShell_, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit, $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactMortier_moyenSol ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusGros, $/GFX/Sound/SoundHappening_ImpactObus_grosVehicule ] ) ),
    ])),
  ([ 'Roquette110Mm130Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHERoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHERoquette110Mm130Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHERoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHERoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHERoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'Roquette110Mm130MmCluster' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270110Mm130MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureRoquetteCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270110Mm130MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270110Mm130MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270110Mm130MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'Roquette110Mm130MmNapalm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolNapalmRoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxOuvertureRoquetteNapalm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolNapalmRoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolNapalmRoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolNapalmRoquette110Mm130Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Napalm_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteBM30300Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureRoquetteCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteBuratino220Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEBuratino220Mm_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEBuratino220Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEBuratino220Mm_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEBuratino220Mm_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEBuratino220Mm_, $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquettes_Buratino_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteLAW' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEPetit, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusMoyen, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
    ])),
  ([ 'RoquetteM24F240Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEM270227Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteM261Hydras' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM261Hydras_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolM261Hydras_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteM261HydrasLight' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM261LightHydras_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolM261LightHydras_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteM26M270227Mm' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHeTresgros2, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEM270227Mm_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227Mm_, $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactArtillerie_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteM26M270227MmCluster' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxOuvertureRoquetteCluster_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolHEM270227MmCluster_, $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Bombe_Cluster_sol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteMRFMinenraumfahrzeug' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolMRFMinenraumfahrzeug_, $/GFX/Sound/SoundHappening_ImpactRoquettes_MRF_lourdeSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolMRFMinenraumfahrzeug_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactSolMRFMinenraumfahrzeug_, $/GFX/Sound/SoundHappening_ImpactRoquettes_MRF_lourdeSol ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactSolMRFMinenraumfahrzeug_, $/GFX/Sound/SoundHappening_ImpactRoquettes_MRF_lourdeSol ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactSolMRFMinenraumfahrzeug_, $/GFX/Sound/SoundHappening_ImpactRoquettes_MRF_lourdeSol ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquettes_MRF_lourdeSol ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteRecoilessGun' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolHEPetit_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEPetit, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolHEPetit_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactMissileVehiculePetit, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactHeVehiculeAirPetit, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ FxRebondObusMoyen, $/GFX/Sound/SoundHappening_ImpactObus_moyenVehicule ] ) ),
    ])),
  ([ 'RoquetteS13' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolS13_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolS13_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteS13Missile' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolS13_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolS13_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeGros_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteS5' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolS5_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolS5_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteS5Light' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolLightS5_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolLightS5_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHePetit_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteSNEB' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM261Hydras_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolM261Hydras_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'RoquetteSNEBLight' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactSolM261LightHydras_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactEauHEGros, $/GFX/Sound/SoundHappening_Impact_HE_Gros_Eau ] ) ),
      ( EImpactSurface/Air , FxImpactSolM261LightHydras_ ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ FxImpactBatHeMoyen_, $/GFX/Sound/SoundHappening_ImpactRoquette_petitBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeM261Hydras, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactVehiculeHeTresgros2, $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_ImpactRoquette_petitVehicule ),
      ( EImpactSurface/Ricochet , nil ),
    ])),
  ([ 'YakBGatling' ], TImpactHappening( Happenings = MAP[
      ( EImpactSurface/Ground , TCompositeHappening( SubHappenings = [ FxImpactM168Gatling_, $/GFX/Sound/SoundHappening_ImpactCanon_petitSol ] ) ),
      ( EImpactSurface/Water , TCompositeHappening( SubHappenings = [ FxImpactBallesCal50Eau_, $/GFX/Sound/SoundHappening_Impact_HE_Petit_Eau ] ) ),
      ( EImpactSurface/Air , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingAir_, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmSol ] ) ),
      ( EImpactSurface/Wall , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmBatiment ] ) ),
      ( EImpactSurface/Vehicle , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM168Gatling, $/GFX/Sound/SoundHappening_ImpactAutocanon_20mmVehicule ] ) ),
      ( EImpactSurface/FlyingVehicle , TCompositeHappening( SubHappenings = [ FxImpactDCAM168GatlingVehiculeAir_, $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ] ) ),
      ( EImpactSurface/Missile , $/GFX/Sound/SoundHappening_FULDA_Impacts_DCA ),
      ( EImpactSurface/Ricochet , TCompositeHappening( SubHappenings = [ OrientationWrapperFxImpactVehiculeM197Gatling, $/GFX/Sound/SoundHappening_ImpactObus_petitVehicule ] ) ),
    ])),
]

